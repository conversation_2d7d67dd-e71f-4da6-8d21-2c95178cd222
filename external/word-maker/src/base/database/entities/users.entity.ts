import { Column, Entity, OneToMany, Unique } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { purchases } from './purchase.entity';
import {
  GenderEnum,
  RoleEnum,
  UserStatusEnum,
  UserTypeEnum,
  VerifiedByEnum,
} from '../../enums/user.enums';
import { devices } from './devices.entity';
import { payments } from './payments.entity';

@Entity()
@Unique(['phone'])
@Unique(['email'])
export class users extends AbstractEntity {
  @Column()
  country_code: string;

  @Column()
  phone: string;

  @Column({ default: 'IR' })
  country_name: string;

  @Column({ nullable: true })
  email: string;

  @Column({ default: 0 })
  confirmation_code: string;

  @Column({ nullable: true })
  name: string;

  @Column({ nullable: true })
  family: string;

  @Column({ type: 'date', nullable: true })
  birth_date: string;

  @Column({
    type: 'enum',
    enum: GenderEnum,
    default: null,
    nullable: true,
  })
  gender: GenderEnum;

  @Column({
    type: 'enum',
    enum: UserTypeEnum,
    default: UserTypeEnum.GENERAL,
  })
  userType: UserTypeEnum;

  @Column({
    type: 'enum',
    enum: VerifiedByEnum,
    default: VerifiedByEnum.PHONE,
    nullable: true,
  })
  verified_by: VerifiedByEnum;

  @Column({ default: UserStatusEnum.PENDING })
  status: UserStatusEnum;

  @Column({
    type: 'enum',
    enum: RoleEnum,
    array: true,
    default: [RoleEnum.USER],
  })
  roles: RoleEnum[];

  @OneToMany(() => purchases, (purchase) => purchase.user)
  purchases: purchases[];

  @OneToMany(() => devices, (device) => device.user)
  devices: devices[];

  @OneToMany(() => payments, (payment) => payment.user)
  payments: payments[];
}
