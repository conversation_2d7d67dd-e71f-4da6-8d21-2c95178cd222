# Role-Based Access Control (RBAC) Implementation

This document describes the implementation of Role-Based Access Control (RBAC) for the Word Maker application.

## Overview

The RBAC system has been implemented to replace the existing phone number-based admin authentication with a more flexible role-based system.

## Changes Made

### 1. Added roles field to users entity

**File: `src/base/enums/user.enums.ts`**
- Added `RoleEnum` with values: `ADMIN`, `AFFILIATE`, `USER`

**File: `src/base/database/entities/users.entity.ts`**
- Added `roles` field as an array of `RoleEnum` with default value `[RoleEnum.USER]`

**File: `src/base/database/migrations/1739084031572-add-user-roles.ts`**
- Migration to add roles column to users table
- Automatically assigns roles based on existing data:
  - Users with `userType = 'MASTER'` → `AFFILIATE` role
  - Users with phone numbers `'9155528224'` or `'9113331643'` → `ADMIN` role
  - All other users → `USER` role

### 2. Updated authentication to include roles in JWT payload

**File: `src/auth/auth.service.ts`**
- Updated `signIn` method to include `roles` in JWT payload
- Updated `refreshToken` method to include `roles` in JWT payload

**File: `src/user/users.dto.ts`**
- Added `roles` field to `UserOutputDto` with proper API documentation

### 3. Created RBAC infrastructure

**File: `src/auth/roles.decorator.ts`**
- Created `@Roles()` decorator that accepts role names as parameters
- Uses `SetMetadata` to store required roles

**File: `src/auth/roles.guard.ts`**
- Implemented `RolesGuard` that:
  - Extracts user from JWT token
  - Checks if user's roles match required roles from `@Roles()` decorator
  - Throws `UnauthorizedException` if access is denied
  - Allows access if no roles are specified

### 4. Applied RBAC to controllers

**File: `src/admin/admin.controller.ts`**
- Replaced `PhoneCheckInterceptor` with `@Roles(RoleEnum.ADMIN)` at controller level
- All admin endpoints now require `ADMIN` role

**File: `src/discount/discount.controller.ts`**
- Replaced `AdminGuard` with `AuthGuard + RolesGuard + @Roles(RoleEnum.ADMIN)`
- Admin-only endpoints: `/all` and `/create`

## Usage Examples

### Protecting a controller with roles
```typescript
@Controller('admin')
@UseGuards(AuthGuard, RolesGuard)
@Roles(RoleEnum.ADMIN)
export class AdminController {
  // All endpoints require ADMIN role
}
```

### Protecting specific endpoints
```typescript
@Get('/admin-only')
@UseGuards(AuthGuard, RolesGuard)
@Roles(RoleEnum.ADMIN)
async adminOnlyEndpoint() {
  // Only users with ADMIN role can access
}

@Get('/affiliate-or-admin')
@UseGuards(AuthGuard, RolesGuard)
@Roles(RoleEnum.AFFILIATE, RoleEnum.ADMIN)
async affiliateOrAdminEndpoint() {
  // Users with AFFILIATE or ADMIN role can access
}
```

## Migration Instructions

1. **Run the migration:**
   ```bash
   npm run migration:run
   ```

2. **Verify role assignments:**
   - Check that existing admin users have `ADMIN` role
   - Check that MASTER users have `AFFILIATE` role
   - Check that regular users have `USER` role

3. **Update environment variables:**
   - The `ADMIN_USERNAMES` environment variable is no longer used
   - Admin access is now controlled by the `roles` field in the database

## Backward Compatibility

- The existing `AdminGuard` and `PhoneCheckInterceptor` are still available but deprecated
- The new RBAC system coexists with the old system during transition
- JWT tokens now include roles, but old tokens without roles will still work (users will have no roles until they log in again)

## Security Considerations

- Roles are stored in the database and included in JWT tokens
- Role changes require users to log in again to get updated tokens
- The `RolesGuard` validates both token authenticity and role permissions
- Multiple roles can be assigned to a single user
- Multiple roles can be required for a single endpoint

## Future Enhancements

- Add role management endpoints for admins to assign/remove roles
- Implement role hierarchy (e.g., ADMIN inherits AFFILIATE permissions)
- Add audit logging for role-based access attempts
- Create middleware to automatically refresh tokens when roles change
