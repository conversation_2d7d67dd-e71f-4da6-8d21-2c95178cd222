import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
  UsePipes,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import {
  DiscountInputDto,
  DiscountOutputDto,
  DiscountPaginationOutputDto,
  DiscountSearchDto,
} from './discount.dto';
import { DiscountService } from './discount.service';
import { DateTransformPipe } from '../base/utils/pipes/date-trasform';
import { AuthGuard } from '../auth/auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { RoleEnum } from '../base/enums/user.enums';
import { TransformDateInterceptor } from '../base/utils/interceptors/date-interceptors';
import { ExpireDateValidationPipe } from '../base/utils/pipes/expire_date-validation';

@Controller('discounts')
@ApiTags('discounts')
@ApiBearerAuth()
export class DiscountController {
  constructor(
    private discountService: DiscountService,
    private readonly dateTransformPipe: DateTransformPipe,
  ) {}

  @Get('/all')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)
  @ApiResponse({
    status: 200,
    description: 'لیست کد های تخفیف با صفحه‌بندی',
    type: DiscountPaginationOutputDto,
  })
  async discounts(
    @Query() discountSearchDto: DiscountSearchDto,
  ): Promise<DiscountPaginationOutputDto> {
    return await this.discountService.getDiscount(discountSearchDto);
  }

  @Post('/create')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)
  @UseInterceptors(MapInterceptor(DiscountOutputDto))
  @UseInterceptors(TransformDateInterceptor)
  @UsePipes(ExpireDateValidationPipe)
  @ApiResponse({
    status: 200,
    description: 'ایجاد کد های تخفیف',
    type: DiscountOutputDto,
    isArray: true,
  })
  async createDiscount(
    @Body() createDiscountDto: DiscountInputDto,
  ): Promise<DiscountOutputDto[]> {
    createDiscountDto.expire_date = this.dateTransformPipe.transform(
      createDiscountDto.expire_date,
    );
    return this.discountService.generateDiscounts(createDiscountDto);
  }
}
