import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import { PaymentsRepository } from './payment.repository';
import { LogService } from '../base/logger/log.service';
import * as ZarinpalCheckout from 'zarinpal-checkout';
import {
  PaymentStateEnum,
  PurchaseStateEnum,
} from '../base/enums/purchase-state.enum';
import {
  CreatePaymentDto,
  CreatePaymentOutputDto,
  PaymentDiscountValidationOutputDto,
  PaymentSaveInputDto,
} from './payment.dto';
import { ProductService } from '../product/product.service';
import { DiscountService } from '../discount/discount.service';
import { PurchasesRepository } from '../purchase/purchase.repository';
import { DiscountStateEnum } from 'src/base/database/entities/discounts.entity';
@Injectable()
export class PaymentService {
  private zarinpal;

  constructor(
    private readonly paymentsRepository: PaymentsRepository,
    private readonly purchasesRepository: PurchasesRepository,
    private readonly logService: LogService,
    private readonly productService: ProductService,
    private readonly discountService: DiscountService,
  ) {
    this.zarinpal = ZarinpalCheckout.create(
      process.env.ZARIN_MERCHANT_ID,
      false,
    ); // true برای sandbox است
  }

  async createPayment(
    createPaymentDto: CreatePaymentDto,
  ): Promise<CreatePaymentOutputDto> {
    try {
      const { product_id, discount_code } = createPaymentDto;
      const product = await this.getProduct(product_id);
      let final_price = product.price;
      let discount_id;

      // Pre-validate discount code if provided
      if (discount_code) {
        const discountValidation =
          await this.discountService.checkDiscountCodeActive({
            code: discount_code,
          });

        if (!discountValidation.success) {
          throw new NotFoundException(discountValidation.message);
        }

        const { discount } = discountValidation;
        discount_id = discount.id;
        const discountAmount = (final_price * discount.discount_percent) / 100;
        final_price -= discountAmount;
      }

      const result = await this.zarinpal.PaymentRequest({
        Amount: final_price / 10,
        CallbackURL: createPaymentDto.callback_url,
        Description: 'خرید فایل APK',
      });

      const { authority, status } = result;
      if (status === 100) {
        // Save payment information to database
        const payment = Object.assign(new PaymentSaveInputDto(), {
          price: product.price,
          final_price,
          product_id,
          discount_id: discount_id ?? null,
          order_id: authority,
          user_id: 0,
          state: PaymentStateEnum.SEND_TO_BANK,
        });

        const savedPayment = await this.paymentsRepository.save(payment);

        return {
          authority,
          url: `https://www.zarinpal.com/pg/StartPay/${authority}`,
          id: savedPayment.id,
        };
      } else {
        this.logService.createLog(
          'Failed to create payment-zarrinPal.',
          result.data,
        );
        throw new Error('Failed to create payment.');
      }
    } catch (e) {
      this.logService.createLog(`Failed to create payment. ${e.message}`, e);
      if (e instanceof HttpException) {
        throw e;
      }
      throw new Error(`Failed to create payment. ${e.message}`);
    }
  }
  private async getProduct(id: number) {
    const product = await this.productService.getProductById(id);
    if (!product) {
      this.logService.createLog('Product not found.', id.toString());
      throw new Error('Product not found.');
    }
    return product;
  }

  async verifyPayment(
    authority: string,
    status: string,
  ): Promise<{ success: boolean; message: string }> {
    const payment = await this.paymentsRepository.findOneByOrderId(authority);
    if (!payment) {
      throw new Error('Payment not found.');
    }
    if (status !== 'OK') {
      payment.state = PaymentStateEnum.UNSUCCESS;
      await this.paymentsRepository.update(payment);
      this.logService.createLog(
        'Payment not successful.',
        `${authority} - ${status}`,
      );
      return { success: false, message: 'پرداخت توسط کاربر لغو شد.' };
    }

    try {
      const result = await this.zarinpal.PaymentVerification({
        Amount: payment.final_price / 10,
        Authority: authority,
      });

      if (result.status === 100) {
        payment.state = PaymentStateEnum.SUCCESS;
        await this.paymentsRepository.update(payment);

        // Save purchase
        await this.savePurchase(
          payment.user_id,
          payment.product_id,
          payment.id,
        );

        // Consume discount if used
        if (payment.discount_id) {
          try {
            // Get discount by ID
            const discount = await this.discountService.getDiscountById(
              payment.discount_id,
            );

            if (discount) {
              await this.discountService.consumeDiscountForPayment(
                discount.code,
                payment.id,
              );
            }
          } catch (error) {
            this.logService.createLog(
              'Failed to consume discount after successful payment',
              error,
            );
            // Don't fail the payment if discount consumption fails
          }
        }

        return { success: true, message: 'پرداخت با موفقیت انجام شد.' };
      } else {
        payment.state = PaymentStateEnum.UNSUCCESS;
        await this.paymentsRepository.update(payment);

        // If payment failed and discount was reserved, rollback
        if (payment.discount_id) {
          try {
            await this.discountService.rollbackDiscountConsumption(
              payment.discount_id,
            );
          } catch (error) {
            this.logService.createLog(
              'Failed to rollback discount after failed payment',
              error,
            );
          }
        }

        return { success: false, message: 'پرداخت موفق نبود.' };
      }
    } catch (e) {
      this.logService.createLog('Payment verification failed.', e);
      throw new Error('Failed to verify payment.');
    }
  }
  private async expireDiscountCode(discount_id: number) {
    await this.discountService.expireDiscount(discount_id);
  }
  private async savePurchase(
    user_id: number,
    product_id: number,
    payment_id: number,
  ) {
    await this.purchasesRepository.save({
      user_id,
      product_id,
      time: new Date(),
      state: PurchaseStateEnum.PURCHASED,
      payment_id,
    });
  }

  async validateDiscountForPayment(
    productId: number,
    discountCode: string,
  ): Promise<PaymentDiscountValidationOutputDto> {
    try {
      // Get product details
      const product = await this.getProduct(productId);
      const originalPrice = product.price;

      // Validate discount code
      const discountValidation =
        await this.discountService.checkDiscountCodeActive({
          code: discountCode,
        });

      if (!discountValidation.success) {
        return {
          success: false,
          message: discountValidation.message,
        };
      }

      const discount = discountValidation.discount;
      const discountAmount = (originalPrice * discount.discount_percent) / 100;
      const finalPrice = originalPrice - discountAmount;

      return {
        success: true,
        message: 'کد تخفیف معتبر است',
        discount_details: {
          code: discount.code,
          percent: discount.discount_percent,
          discount_amount: discountAmount,
          original_price: originalPrice,
          final_price: finalPrice,
          currency: product.currency,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'خطا در اعتبارسنجی کد تخفیف',
      };
    }
  }

  async getPayment(id: number, user_id: number) {
    const purchases = await this.paymentsRepository.findOneById(id, user_id);
    return purchases;
  }

  async getPaymentWithDiscountDetails(id: number, user_id: number) {
    const payment = await this.paymentsRepository.findOneById(id, user_id);
    if (!payment) {
      throw new Error('Payment not found.');
    }

    // Add discount details if discount was used
    if (payment.discount) {
      const discountAmount =
        (payment.price * payment.discount.discount_percent) / 100;
      return {
        ...payment,
        discount_code: payment.discount.code,
        discount_percent: payment.discount.discount_percent,
        discount_amount: discountAmount,
      };
    }

    return payment;
  }

  async getPaymentHistory(user_id: number) {
    const payments = await this.paymentsRepository.findByUserId(user_id);

    return payments.map((payment) => {
      if (payment.discount) {
        const discountAmount =
          (payment.price * payment.discount.discount_percent) / 100;
        return {
          ...payment,
          discount_code: payment.discount.code,
          discount_percent: payment.discount.discount_percent,
          discount_amount: discountAmount,
        };
      }
      return payment;
    });
  }
}
