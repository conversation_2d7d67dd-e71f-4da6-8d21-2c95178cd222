import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import {
  discounts,
  DiscountStateEnum,
  DiscountTypeEnum,
} from '../base/database/entities/discounts.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';

export class DiscountSearchDto {
  @IsEnum(DiscountStateEnum, {
    message:
      'وضعیت کد تخفیف باید یکی از مقادیر ACTIVE یا DEACTIVE یا USED یا UNUSED باشد',
  })
  @ApiPropertyOptional({
    enum: DiscountStateEnum,
    example: 'ACTIVE',
    description: 'وضعیت کد تخفیف',
  })
  @IsOptional()
  state?: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiPropertyOptional({
    enum: DiscountTypeEnum,
    example: 'simple',
    description: 'نوع کد تخفیف',
  })
  @IsOptional()
  @Mapped()
  type?: DiscountTypeEnum;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Mapped()
  referral_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Mapped()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  code?: string;

  @ApiPropertyOptional({
    description: 'Created date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  created_date_from?: string;

  @ApiPropertyOptional({
    description: 'Created date to (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDateString()
  created_date_to?: string;

  @ApiPropertyOptional({
    description: 'Expire date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  expire_date_from?: string;

  @ApiPropertyOptional({
    description: 'Expire date to (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDateString()
  expire_date_to?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination (starts from 1)',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;
}

export class DiscountOutputDto implements MappedWithEntity<discounts> {
  @ApiProperty()
  @Mapped()
  id: number;

  @ApiProperty()
  @Mapped()
  code: string;

  @ApiProperty({ description: 'درصد تخفیف' })
  @Mapped()
  discount_percent: number;
  @ApiProperty({ description: 'تاریخ انقضا' })
  @Mapped()
  expire_date?: Date;
  @ApiProperty({
    example: 'active',
    description: 'وضعیت کد تخفیف ',
    enum: DiscountStateEnum,
  })
  @Mapped()
  state?: DiscountStateEnum;
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @Mapped()
  type?: DiscountTypeEnum;
  @ApiProperty({ description: 'شناسه ارجاع' })
  @Mapped()
  referral_id?: number;
  @ApiProperty({ description: 'توضیحات' })
  @Mapped()
  description?: string;
}

export class DiscountInputDto {
  @ApiProperty({ example: 'YYYY-MM-DD', description: 'تاریخ انقضای کد تخفیف' })
  @IsOptional()
  @IsDateString()
  expire_date: string;

  @ApiProperty({ example: 'ACTIVE', description: 'وضعیت کد تخفیف ' })
  @IsString()
  @IsOptional()
  prefix?: string;

  @ApiProperty()
  @IsNumber()
  discount_percent: number;

  @ApiProperty()
  @IsNumber()
  count: number;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;
}

export class DiscountSaveDto {
  @ApiProperty()
  @IsOptional()
  expire_date?: string;

  @ApiProperty({ example: '10', description: 'درصد تخفیف' })
  @IsNumber()
  discount_percent: number;

  @IsEnum(DiscountStateEnum)
  @ApiProperty({ example: 'active', description: 'وضعیت' })
  @IsNotEmpty()
  state: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'code', description: 'کد تخفیف' })
  @IsString()
  code: string;
}

export class UpdateDiscountInputDto {
  id: number;
  state?: DiscountStateEnum;
  paymentId: number;
}

export class ConsumeDiscountInputDto {
  @ApiProperty({ example: 'ABCD', description: 'کد تخفیف' })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class ConsumeDiscountOutputDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  discount?: DiscountOutputDto;
}

export class CheckDiscountInputDto {
  @ApiProperty({ example: 'ABCD12', description: 'کد تخفیف' })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class CheckDiscountOutputDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  discount?: DiscountOutputDto;
}

export class DiscountPaginationOutputDto {
  @ApiProperty({ type: [DiscountOutputDto] })
  data: DiscountOutputDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty({ description: 'Count of active discounts' })
  activeCount: number;

  @ApiProperty({ description: 'Count of used discounts' })
  usedCount: number;

  @ApiProperty({ description: 'Count of expired discounts' })
  expiredCount: number;
}
